import { CartItem, Product } from '../types';

// In-memory cart for development
let cart: CartItem[] = [];

export const cartService = {
  /**
   * Get all items in the cart
   */
  getCart: async (): Promise<CartItem[]> => {
    return Promise.resolve([...cart]);
  },

  /**
   * Add a product to the cart
   */
  addToCart: async (product: Product, quantity: number = 1): Promise<CartItem[]> => {
    const existingItemIndex = cart.findIndex(item => item.product.id === product.id);
    
    if (existingItemIndex >= 0) {
      // Update quantity if item already exists
      cart[existingItemIndex].quantity += quantity;
    } else {
      // Add new item
      cart.push({ product, quantity });
    }
    
    return Promise.resolve([...cart]);
  },

  /**
   * Remove a product from the cart
   */
  removeFromCart: async (productId: string): Promise<CartItem[]> => {
    cart = cart.filter(item => item.product.id !== productId);
    return Promise.resolve([...cart]);
  },

  /**
   * Update the quantity of a product in the cart
   */
  updateQuantity: async (productId: string, quantity: number): Promise<CartItem[]> => {
    const itemIndex = cart.findIndex(item => item.product.id === productId);
    
    if (itemIndex >= 0) {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        cart = cart.filter(item => item.product.id !== productId);
      } else {
        // Update quantity
        cart[itemIndex].quantity = quantity;
      }
    }
    
    return Promise.resolve([...cart]);
  },

  /**
   * Clear the cart
   */
  clearCart: async (): Promise<CartItem[]> => {
    cart = [];
    return Promise.resolve([]);
  },

  /**
   * Calculate the total price of items in the cart
   */
  getCartTotal: async (): Promise<number> => {
    const total = cart.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity);
    }, 0);
    
    return Promise.resolve(total);
  }
}; 