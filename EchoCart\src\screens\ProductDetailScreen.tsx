import React, { useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import {
  ProductDetailScreenRouteProp,
  ProductDetailScreenNavigationProp,
} from '../navigation/types';
import { MicrophoneButton, TranscriptDisplay } from '../components';
import { useVoiceRecognition, useCart } from '../hooks';
import { voiceService } from '../services';
import { ParsedVoiceCommand } from '../types';

export const ProductDetailScreen: React.FC = () => {
  const route = useRoute<ProductDetailScreenRouteProp>();
  const navigation = useNavigation<ProductDetailScreenNavigationProp>();
  const { product } = route.params;
  const { addToCart } = useCart();
  const {
    isListening,
    transcript,
    state,
    startListening,
    stopListening,
    parsedCommand,
    resetCommand,
  } = useVoiceRecognition();

  // Handle voice commands
  React.useEffect(() => {
    if (parsedCommand) {
      handleVoiceCommand(parsedCommand);
    }
  }, [parsedCommand]);

  const handleVoiceCommand = useCallback(async (command: ParsedVoiceCommand) => {
    switch (command.type) {
      case 'add_to_cart':
        await addToCart(product);
        voiceService.speak(`Added ${product.name} to your cart`);
        break;
      case 'view_cart':
        navigation.navigate('Cart');
        voiceService.speak('Opening your cart');
        break;
      case 'help':
        voiceService.speak(
          'You can say: add to cart, view cart, or go back'
        );
        break;
      case 'cancel':
        navigation.goBack();
        voiceService.speak('Going back');
        break;
      default:
        voiceService.speak("I'm sorry, I didn't understand that command");
        break;
    }

    resetCommand();
  }, [product, addToCart, navigation, resetCommand]);

  const handleMicPress = useCallback(async () => {
    if (isListening) {
      await stopListening();
    } else {
      await startListening();
    }
  }, [isListening, startListening, stopListening]);

  const handleAddToCart = useCallback(() => {
    addToCart(product);
    voiceService.speak(`Added ${product.name} to your cart`);
  }, [product, addToCart]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <Image
          source={{ uri: product.imageUrl }}
          style={styles.image}
          resizeMode="cover"
        />
        <View style={styles.contentContainer}>
          <Text style={styles.name}>{product.name}</Text>
          <Text style={styles.price}>${product.price.toFixed(2)}</Text>
          <Text style={styles.category}>Category: {product.category}</Text>
          <Text style={styles.description}>{product.description}</Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.addToCartButton}
          onPress={handleAddToCart}
        >
          <Ionicons name="cart" size={20} color="#FFFFFF" />
          <Text style={styles.addToCartText}>Add to Cart</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.floatingButtonContainer}>
        <MicrophoneButton state={state} onPress={handleMicPress} />
      </View>

      <TranscriptDisplay transcript={transcript} isVisible={isListening} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  image: {
    width: '100%',
    height: 300,
    backgroundColor: '#F5F5F5',
  },
  contentContainer: {
    padding: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333333',
  },
  price: {
    fontSize: 20,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  category: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333333',
    marginBottom: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
  },
  addToCartButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  floatingButtonContainer: {
    position: 'absolute',
    bottom: 80,
    alignSelf: 'center',
  },
}); 