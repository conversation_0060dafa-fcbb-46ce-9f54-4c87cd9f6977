import { Product } from '../types';
import { mockProducts } from './mockData';

export const productService = {
  /**
   * Get all products
   */
  getProducts: async (): Promise<Product[]> => {
    // In a real app, this would fetch from an API or Firebase
    return Promise.resolve(mockProducts);
  },

  /**
   * Search products by query
   */
  searchProducts: async (query: string): Promise<Product[]> => {
    // Simple search implementation for mock data
    const normalizedQuery = query.toLowerCase().trim();
    
    return Promise.resolve(
      mockProducts.filter(product => 
        product.name.toLowerCase().includes(normalizedQuery) || 
        product.description.toLowerCase().includes(normalizedQuery) ||
        product.category.toLowerCase().includes(normalizedQuery)
      )
    );
  },

  /**
   * Get product by ID
   */
  getProductById: async (id: string): Promise<Product | null> => {
    const product = mockProducts.find(p => p.id === id);
    return Promise.resolve(product || null);
  },

  /**
   * Get products by category
   */
  getProductsByCategory: async (category: string): Promise<Product[]> => {
    return Promise.resolve(
      mockProducts.filter(product => 
        product.category.toLowerCase() === category.toLowerCase()
      )
    );
  }
}; 