import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import {
  PaymentFailedScreenRouteProp,
  PaymentFailedScreenNavigationProp,
} from '../navigation/types';
import { voiceService } from '../services';

export const PaymentFailedScreen: React.FC = () => {
  const route = useRoute<PaymentFailedScreenRouteProp>();
  const navigation = useNavigation<PaymentFailedScreenNavigationProp>();
  const { reference, message } = route.params;

  // Speak a failure message when the screen loads
  useEffect(() => {
    voiceService.speak('Payment failed. Please try again or use a different payment method.');
  }, []);

  const handleTryAgain = () => {
    navigation.navigate('Checkout');
  };

  const handleGoToCart = () => {
    navigation.navigate('Cart');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="close-circle" size={100} color="#FF3B30" />
        </View>
        <Text style={styles.title}>Payment Failed</Text>
        <Text style={styles.message}>
          Sorry, there was an issue processing your payment. Please try again or use a different payment method.
        </Text>
        <Text style={styles.errorMessage}>{message}</Text>
        <Text style={styles.reference}>Reference: {reference}</Text>
        <TouchableOpacity
          style={styles.tryAgainButton}
          onPress={handleTryAgain}
        >
          <Text style={styles.buttonText}>Try Again</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cartButton}
          onPress={handleGoToCart}
        >
          <Text style={styles.cartButtonText}>Return to Cart</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333333',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#666666',
    lineHeight: 24,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
    color: '#FF3B30',
  },
  reference: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 32,
  },
  tryAgainButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 12,
    width: '80%',
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cartButton: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: '#007AFF',
    width: '80%',
    alignItems: 'center',
  },
  cartButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
}); 