import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import {
  PaymentSuccessScreenRouteProp,
  PaymentSuccessScreenNavigationProp,
} from '../navigation/types';
import { voiceService } from '../services';

export const PaymentSuccessScreen: React.FC = () => {
  const route = useRoute<PaymentSuccessScreenRouteProp>();
  const navigation = useNavigation<PaymentSuccessScreenNavigationProp>();
  const { reference } = route.params;

  // Speak a success message when the screen loads
  useEffect(() => {
    voiceService.speak('Payment successful! Thank you for your order.');
  }, []);

  const handleContinueShopping = () => {
    navigation.navigate('Home');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={100} color="#4CD964" />
        </View>
        <Text style={styles.title}>Payment Successful!</Text>
        <Text style={styles.message}>
          Thank you for your order. Your payment has been processed successfully.
        </Text>
        <Text style={styles.reference}>Order Reference: {reference}</Text>
        <TouchableOpacity
          style={styles.button}
          onPress={handleContinueShopping}
        >
          <Text style={styles.buttonText}>Continue Shopping</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333333',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#666666',
    lineHeight: 24,
  },
  reference: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 32,
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 16,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
}); 