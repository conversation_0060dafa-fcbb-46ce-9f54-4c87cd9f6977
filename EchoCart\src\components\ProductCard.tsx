import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Product } from '../types';

interface ProductCardProps {
  product: Product;
  onPress: () => void;
  onAddToCart: () => void;
}

// Use memo to prevent unnecessary re-renders
export const ProductCard = memo(({ product, onPress, onAddToCart }: ProductCardProps) => {
  const isOlderDevice = Platform.OS === 'ios' && parseInt(Platform.Version as string, 10) < 13;
  
  // Simplified rendering for older devices
  if (isOlderDevice) {
    return (
      <TouchableOpacity style={styles.containerSimple} onPress={onPress}>
        <View style={styles.contentSimple}>
          <Text style={styles.nameSimple}>{product.name}</Text>
          <Text style={styles.priceSimple}>${product.price.toFixed(2)}</Text>
          <TouchableOpacity 
            style={styles.addButtonSimple}
            onPress={(e) => {
              e.stopPropagation();
              onAddToCart();
            }}
          >
            <Text style={styles.addButtonTextSimple}>Add to Cart</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  }
  
  // Regular rendering for newer devices
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Image 
        source={{ uri: product.imageUrl }} 
        style={styles.image}
        defaultSource={require('../../assets/favicon.png')}
      />
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={1}>{product.name}</Text>
        <Text style={styles.price}>${product.price.toFixed(2)}</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={(e) => {
            e.stopPropagation();
            onAddToCart();
          }}
        >
          <Ionicons name="add-circle" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    width: '48%',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  image: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  content: {
    padding: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
    color: '#007AFF',
  },
  addButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: '#007AFF',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Simplified styles for older devices
  containerSimple: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    padding: 12,
    marginBottom: 8,
    width: '100%',
  },
  contentSimple: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  nameSimple: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  priceSimple: {
    fontSize: 14,
    fontWeight: '700',
    color: '#007AFF',
    marginHorizontal: 8,
  },
  addButtonSimple: {
    backgroundColor: '#007AFF',
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  addButtonTextSimple: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
}); 