import React, { useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { CartScreenNavigationProp } from '../navigation/types';
import { CartItemComponent, MicrophoneButton, TranscriptDisplay } from '../components';
import { useVoiceRecognition, useCart } from '../hooks';
import { voiceService } from '../services';
import { ParsedVoiceCommand, CartItem } from '../types';

export const CartScreen: React.FC = () => {
  const navigation = useNavigation<CartScreenNavigationProp>();
  const { cart, total, removeFromCart, updateQuantity, clearCart } = useCart();
  const {
    isListening,
    transcript,
    state,
    startListening,
    stopListening,
    parsedCommand,
    resetCommand,
  } = useVoiceRecognition();

  // Handle voice commands
  React.useEffect(() => {
    if (parsedCommand) {
      handleVoiceCommand(parsedCommand);
    }
  }, [parsedCommand]);

  const handleVoiceCommand = useCallback(async (command: ParsedVoiceCommand) => {
    switch (command.type) {
      case 'checkout':
        if (cart.length > 0) {
          navigation.navigate('Checkout');
          voiceService.speak('Taking you to checkout');
        } else {
          voiceService.speak('Your cart is empty');
        }
        break;
      case 'remove_from_cart':
        if (command.payload?.productName && cart.length > 0) {
          const productName = command.payload.productName.toLowerCase();
          const item = cart.find(
            (item) => item.product.name.toLowerCase().includes(productName)
          );
          if (item) {
            await removeFromCart(item.product.id);
            voiceService.speak(`Removed ${item.product.name} from your cart`);
          } else {
            voiceService.speak(`Sorry, I couldn't find ${command.payload.productName} in your cart`);
          }
        }
        break;
      case 'cancel':
        navigation.goBack();
        voiceService.speak('Going back');
        break;
      case 'help':
        voiceService.speak(
          'You can say: checkout, remove item, or go back'
        );
        break;
      default:
        voiceService.speak("I'm sorry, I didn't understand that command");
        break;
    }

    resetCommand();
  }, [cart, navigation, removeFromCart, resetCommand]);

  const handleMicPress = useCallback(async () => {
    if (isListening) {
      await stopListening();
    } else {
      await startListening();
    }
  }, [isListening, startListening, stopListening]);

  const handleIncreaseQuantity = useCallback((productId: string) => {
    const item = cart.find((item) => item.product.id === productId);
    if (item) {
      updateQuantity(productId, item.quantity + 1);
    }
  }, [cart, updateQuantity]);

  const handleDecreaseQuantity = useCallback((productId: string) => {
    const item = cart.find((item) => item.product.id === productId);
    if (item && item.quantity > 1) {
      updateQuantity(productId, item.quantity - 1);
    } else {
      removeFromCart(productId);
    }
  }, [cart, updateQuantity, removeFromCart]);

  const renderItem = ({ item }: { item: CartItem }) => (
    <CartItemComponent
      item={item}
      onIncreaseQuantity={handleIncreaseQuantity}
      onDecreaseQuantity={handleDecreaseQuantity}
      onRemove={removeFromCart}
    />
  );

  const renderEmptyCart = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="cart-outline" size={64} color="#CCCCCC" />
      <Text style={styles.emptyText}>Your cart is empty</Text>
      <TouchableOpacity
        style={styles.continueShoppingButton}
        onPress={() => navigation.navigate('Home')}
      >
        <Text style={styles.continueShoppingText}>Continue Shopping</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {cart.length > 0 ? (
        <>
          <FlatList
            data={cart}
            renderItem={renderItem}
            keyExtractor={(item) => item.product.id}
            contentContainerStyle={styles.listContent}
          />
          <View style={styles.footer}>
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalAmount}>${total.toFixed(2)}</Text>
            </View>
            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={() => navigation.navigate('Checkout')}
            >
              <Text style={styles.checkoutText}>Proceed to Checkout</Text>
              <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </>
      ) : (
        renderEmptyCart()
      )}

      <View style={styles.floatingButtonContainer}>
        <MicrophoneButton state={state} onPress={handleMicPress} />
      </View>

      <TranscriptDisplay transcript={transcript} isVisible={isListening} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  listContent: {
    padding: 16,
  },
  footer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333333',
  },
  totalAmount: {
    fontSize: 20,
    fontWeight: '600',
    color: '#007AFF',
  },
  checkoutButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  checkoutText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#999999',
    marginTop: 16,
    marginBottom: 24,
  },
  continueShoppingButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  continueShoppingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  floatingButtonContainer: {
    position: 'absolute',
    bottom: 80,
    alignSelf: 'center',
  },
}); 