import React, { useState } from 'react';
import { TouchableOpacity, View, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { VoiceRecognitionState } from '../services/voiceService';

interface MicrophoneButtonProps {
  state: VoiceRecognitionState;
  onPress: () => void;
  size?: number;
}

export const MicrophoneButton: React.FC<MicrophoneButtonProps> = ({
  state,
  onPress,
  size = 60,
}) => {
  // Determine the icon and color based on the state
  const getIconConfig = () => {
    switch (state) {
      case VoiceRecognitionState.LISTENING:
        return { name: 'mic', color: '#FF3B30' };
      case VoiceRecognitionState.PROCESSING:
        return { name: 'mic', color: '#007AFF' };
      case VoiceRecognitionState.ERROR:
        return { name: 'alert-circle', color: '#FF3B30' };
      case VoiceRecognitionState.IDLE:
      default:
        return { name: 'mic', color: '#007AFF' };
    }
  };

  const { name, color } = getIconConfig();
  const isProcessing = state === VoiceRecognitionState.PROCESSING;

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      disabled={isProcessing}
      style={[
        styles.container,
        { width: size, height: size, borderRadius: size / 2, backgroundColor: color },
      ]}
    >
      {isProcessing ? (
        <ActivityIndicator color="#FFFFFF" size="small" />
      ) : (
        <Ionicons name={name as any} size={size / 2} color="#FFFFFF" />
      )}
      
      {state === VoiceRecognitionState.LISTENING && (
        <View style={styles.pulseRing} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  pulseRing: {
    position: 'absolute',
    width: '150%',
    height: '150%',
    borderRadius: 999,
    borderWidth: 2,
    borderColor: '#FF3B30',
    opacity: 0.5,
  },
}); 