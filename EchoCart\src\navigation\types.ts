import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Product } from '../types';

// Define the screens in our app
export type RootStackParamList = {
  Home: undefined;
  ProductDetail: { product: Product };
  Cart: undefined;
  Checkout: undefined;
  PaymentSuccess: { reference: string };
  PaymentFailed: { reference: string; message: string };
};

// Export types for navigation props
export type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;
export type ProductDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ProductDetail'>;
export type CartScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Cart'>;
export type CheckoutScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Checkout'>;
export type PaymentSuccessScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PaymentSuccess'>;
export type PaymentFailedScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PaymentFailed'>;

// Export types for route props
export type HomeScreenRouteProp = RouteProp<RootStackParamList, 'Home'>;
export type ProductDetailScreenRouteProp = RouteProp<RootStackParamList, 'ProductDetail'>;
export type CartScreenRouteProp = RouteProp<RootStackParamList, 'Cart'>;
export type CheckoutScreenRouteProp = RouteProp<RootStackParamList, 'Checkout'>;
export type PaymentSuccessScreenRouteProp = RouteProp<RootStackParamList, 'PaymentSuccess'>;
export type PaymentFailedScreenRouteProp = RouteProp<RootStackParamList, 'PaymentFailed'>; 