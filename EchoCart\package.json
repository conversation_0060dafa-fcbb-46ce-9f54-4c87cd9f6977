{"name": "expo-template-blank-typescript", "description": "The Blank project template includes the minimum dependencies to run and an empty root component.", "license": "0BSD", "version": "53.0.36", "main": "index.ts", "scripts": {"start": "expo start", "start:tunnel": "expo start --tunnel", "start:ngrok": "node scripts/start-with-ngrok.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/webpack-config": "^19.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~49.0.15", "expo-asset": "~8.10.1", "expo-av": "~13.4.1", "expo-dev-client": "^5.2.4", "expo-modules-autolinking": "^2.1.13", "expo-modules-core": "~1.5.12", "expo-speech": "~11.3.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^10.7.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-web": "~0.19.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@types/react": "~18.2.14", "babel-plugin-transform-remove-console": "^6.9.4", "ngrok": "^5.0.0-beta.2", "typescript": "^5.1.3"}}