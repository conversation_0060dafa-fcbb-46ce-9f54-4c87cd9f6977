import { Product } from '../types';

export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Headphones',
    description: 'Premium noise-cancelling wireless headphones with 30-hour battery life',
    price: 199.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'electronics',
  },
  {
    id: '2',
    name: 'Running Shoes',
    description: 'Lightweight running shoes with responsive cushioning',
    price: 89.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'sports',
  },
  {
    id: '3',
    name: 'Smart Watch',
    description: 'Fitness tracker with heart rate monitoring and GPS',
    price: 149.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'electronics',
  },
  {
    id: '4',
    name: 'Cotton T-Shirt',
    description: 'Soft, breathable cotton t-shirt in multiple colors',
    price: 19.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'clothing',
  },
  {
    id: '5',
    name: 'Stainless Steel Water Bottle',
    description: 'Double-walled insulated water bottle that keeps drinks cold for 24 hours',
    price: 24.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'accessories',
  },
  {
    id: '6',
    name: 'Wireless Earbuds',
    description: 'True wireless earbuds with touch controls and charging case',
    price: 79.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'electronics',
  },
  {
    id: '7',
    name: 'Yoga Mat',
    description: 'Non-slip yoga mat with alignment lines',
    price: 34.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'sports',
  },
  {
    id: '8',
    name: 'Backpack',
    description: 'Durable backpack with laptop compartment and multiple pockets',
    price: 49.99,
    imageUrl: 'https://via.placeholder.com/300',
    category: 'accessories',
  },
]; 