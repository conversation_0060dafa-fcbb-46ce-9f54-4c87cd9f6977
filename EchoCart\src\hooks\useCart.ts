import { useState, useCallback, useEffect } from 'react';
import { CartItem, Product } from '../types';
import { cartService } from '../services';

interface UseCartResult {
  cart: CartItem[];
  total: number;
  addToCart: (product: Product, quantity?: number) => Promise<void>;
  removeFromCart: (productId: string) => Promise<void>;
  updateQuantity: (productId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  isInCart: (productId: string) => boolean;
  getQuantity: (productId: string) => number;
}

export const useCart = (): UseCartResult => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  
  // Load cart on mount
  useEffect(() => {
    const loadCart = async () => {
      const cartItems = await cartService.getCart();
      setCart(cartItems);
      
      const cartTotal = await cartService.getCartTotal();
      setTotal(cartTotal);
    };
    
    loadCart();
  }, []);
  
  const addToCart = useCallback(async (product: Product, quantity: number = 1) => {
    const updatedCart = await cartService.addToCart(product, quantity);
    setCart(updatedCart);
    
    const updatedTotal = await cartService.getCartTotal();
    setTotal(updatedTotal);
  }, []);
  
  const removeFromCart = useCallback(async (productId: string) => {
    const updatedCart = await cartService.removeFromCart(productId);
    setCart(updatedCart);
    
    const updatedTotal = await cartService.getCartTotal();
    setTotal(updatedTotal);
  }, []);
  
  const updateQuantity = useCallback(async (productId: string, quantity: number) => {
    const updatedCart = await cartService.updateQuantity(productId, quantity);
    setCart(updatedCart);
    
    const updatedTotal = await cartService.getCartTotal();
    setTotal(updatedTotal);
  }, []);
  
  const clearCart = useCallback(async () => {
    await cartService.clearCart();
    setCart([]);
    setTotal(0);
  }, []);
  
  const isInCart = useCallback((productId: string) => {
    return cart.some(item => item.product.id === productId);
  }, [cart]);
  
  const getQuantity = useCallback((productId: string) => {
    const item = cart.find(item => item.product.id === productId);
    return item ? item.quantity : 0;
  }, [cart]);
  
  return {
    cart,
    total,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    isInCart,
    getQuantity,
  };
}; 