# EchoCart - Voice-Activated Shopping Assistant

EchoCart is a mobile application that allows users to search for and purchase products using voice commands. It provides a hands-free e-commerce experience with voice-based navigation, product discovery, and checkout.

## Features

- **Voice-to-Text Conversion**: Convert user speech into accurate text commands
- **Product Search**: Search products using voice commands
- **Voice Navigation**: Control the app with voice for browsing, selection, and cart management
- **Voice-Based Checkout**: Complete checkout using only voice commands
- **English Language Support**: Optimized for English voice commands and responses

## Tech Stack

- **Frontend**: React Native with TypeScript
- **Voice Recognition**: Expo Speech and Audio APIs
- **Backend**: Firebase (Auth + Firestore)
- **Payments**: Simulated payment flow (Paystack integration ready)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Expo CLI

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/echocart.git
cd echocart
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Start the development server
```bash
npx expo start
```

## Usage

- Tap the microphone button to activate voice commands
- Say commands like:
  - "Search for wireless headphones"
  - "Add to cart"
  - "Show my cart"
  - "Checkout now"
  - "Confirm payment"

## Project Structure

```
EchoCart/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # Screen components
│   ├── services/       # Business logic and API services
│   ├── hooks/          # Custom React hooks
│   ├── navigation/     # Navigation configuration
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   └── config/         # Configuration files
├── assets/             # Images, fonts, etc.
└── App.tsx             # Entry point
```

## Voice Commands

- **Search**: "Show me [product]", "Search for [product]", "Find [product]"
- **Cart Management**: "Add [product] to cart", "Remove [product]", "View cart"
- **Checkout**: "Checkout", "Pay now", "Buy now"
- **Navigation**: "Go back", "Cancel"
- **Help**: "Help"

## License

This project is licensed under the MIT License - see the LICENSE file for details. 