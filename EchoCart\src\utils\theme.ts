import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const COLORS = {
  // Primary colors
  primary: '#007AFF',
  primaryDark: '#0062CC',
  primaryLight: '#66AFFF',
  
  // Secondary colors
  secondary: '#FF3B30',
  secondaryDark: '#CC2F26',
  secondaryLight: '#FF8079',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9A9A9A',
  lightGray: '#F5F5F5',
  darkGray: '#333333',
  
  // Status colors
  success: '#4CD964',
  warning: '#FF9500',
  error: '#FF3B30',
  info: '#5AC8FA',
  
  // Background
  background: '#F5F5F5',
  card: '#FFFFFF',
};

export const SIZES = {
  // Global sizes
  base: 8,
  font: 14,
  radius: 8,
  padding: 16,
  
  // Font sizes
  h1: 30,
  h2: 24,
  h3: 18,
  h4: 16,
  body1: 16,
  body2: 14,
  body3: 12,
  
  // App dimensions
  width,
  height,
};

export const FONTS = {
  h1: {
    fontSize: SIZES.h1,
    fontWeight: '700',
    color: COLORS.darkGray,
  },
  h2: {
    fontSize: SIZES.h2,
    fontWeight: '600',
    color: COLORS.darkGray,
  },
  h3: {
    fontSize: SIZES.h3,
    fontWeight: '600',
    color: COLORS.darkGray,
  },
  h4: {
    fontSize: SIZES.h4,
    fontWeight: '500',
    color: COLORS.darkGray,
  },
  body1: {
    fontSize: SIZES.body1,
    fontWeight: '400',
    color: COLORS.darkGray,
  },
  body2: {
    fontSize: SIZES.body2,
    fontWeight: '400',
    color: COLORS.darkGray,
  },
  body3: {
    fontSize: SIZES.body3,
    fontWeight: '400',
    color: COLORS.gray,
  },
};

export const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
  large: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
};

export const SPACING = {
  xs: SIZES.base / 2, // 4
  s: SIZES.base, // 8
  m: SIZES.base * 2, // 16
  l: SIZES.base * 3, // 24
  xl: SIZES.base * 4, // 32
  xxl: SIZES.base * 5, // 40
};

const theme = { COLORS, SIZES, FONTS, SHADOWS, SPACING };

export default theme; 