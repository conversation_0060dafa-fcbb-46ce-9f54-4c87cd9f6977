import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { CheckoutScreenNavigationProp } from '../navigation/types';
import { MicrophoneButton, TranscriptDisplay } from '../components';
import { useVoiceRecognition, useCart } from '../hooks';
import { voiceService, paymentService } from '../services';
import { ParsedVoiceCommand } from '../types';

export const CheckoutScreen: React.FC = () => {
  const navigation = useNavigation<CheckoutScreenNavigationProp>();
  const { cart, total, clearCart } = useCart();
  const {
    isListening,
    transcript,
    state,
    startListening,
    stopListening,
    parsedCommand,
    resetCommand,
  } = useVoiceRecognition();

  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentConfig, setPaymentConfig] = useState<any>(null);

  // Handle voice commands
  React.useEffect(() => {
    if (parsedCommand) {
      handleVoiceCommand(parsedCommand);
    }
  }, [parsedCommand]);

  const handleVoiceCommand = useCallback(async (command: ParsedVoiceCommand) => {
    switch (command.type) {
      case 'confirm_payment':
        handlePayment();
        break;
      case 'cancel':
        navigation.goBack();
        voiceService.speak('Going back to your cart');
        break;
      case 'help':
        voiceService.speak(
          'You can say: confirm payment, or cancel'
        );
        break;
      default:
        voiceService.speak("I'm sorry, I didn't understand that command");
        break;
    }

    resetCommand();
  }, [navigation, resetCommand]);

  const handleMicPress = useCallback(async () => {
    if (isListening) {
      await stopListening();
    } else {
      await startListening();
    }
  }, [isListening, startListening, stopListening]);

  const handlePayment = async () => {
    if (!email || !name || !address || !city) {
      voiceService.speak('Please fill in all required fields');
      return;
    }

    try {
      // In a real app, we would use a real payment gateway
      // For now, we'll simulate a successful payment
      const reference = `echocart-${Date.now()}`;
      
      // Simulate payment processing
      setTimeout(() => {
        paymentService.processSuccessfulPayment(reference);
        navigation.navigate('PaymentSuccess', { reference });
      }, 1000);
      
      voiceService.speak('Processing your payment');
    } catch (error) {
      console.error('Error processing payment:', error);
      voiceService.speak('Sorry, there was an error processing your payment');
      
      // Navigate to payment failed screen
      navigation.navigate('PaymentFailed', { 
        reference: `echocart-${Date.now()}`,
        message: 'Payment processing error'
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder="Your email address"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Full Name</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Your full name"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shipping Address</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Address</Text>
            <TextInput
              style={styles.input}
              value={address}
              onChangeText={setAddress}
              placeholder="Street address"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>City</Text>
            <TextInput
              style={styles.input}
              value={city}
              onChangeText={setCity}
              placeholder="City"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Items ({cart.length})</Text>
            <Text style={styles.summaryValue}>${total.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Shipping</Text>
            <Text style={styles.summaryValue}>$0.00</Text>
          </View>
          <View style={styles.divider} />
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, styles.totalLabel]}>Total</Text>
            <Text style={[styles.summaryValue, styles.totalValue]}>
              ${total.toFixed(2)}
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.payButton}
          onPress={handlePayment}
        >
          <Text style={styles.payButtonText}>Pay Now</Text>
          <Ionicons name="card-outline" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.floatingButtonContainer}>
        <MicrophoneButton state={state} onPress={handleMicPress} />
      </View>

      <TranscriptDisplay transcript={transcript} isVisible={isListening} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333333',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666666',
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666666',
  },
  summaryValue: {
    fontSize: 16,
    color: '#333333',
  },
  divider: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
  },
  footer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  payButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  payButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  floatingButtonContainer: {
    position: 'absolute',
    bottom: 80,
    alignSelf: 'center',
  },
}); 