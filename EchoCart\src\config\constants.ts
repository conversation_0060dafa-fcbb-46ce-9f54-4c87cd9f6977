// App configuration constants
import { isLowPerformanceDevice } from './performance';

// API Keys (replace with your actual keys in production)
export const FIREBASE_CONFIG = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_AUTH_DOMAIN",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_STORAGE_BUCKET",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
};

export const PAYSTACK_PUBLIC_KEY = "YOUR_PAYSTACK_PUBLIC_KEY";

// App settings
export const APP_SETTINGS = {
  // Voice recognition settings
  voice: {
    recordingDuration: isLowPerformanceDevice() ? 3000 : 5000, // shorter for older devices
    language: 'en-US',
    pitch: 1.0,
    rate: 0.9,
    // Disable voice features on low performance devices
    enabled: !isLowPerformanceDevice(),
  },
  
  // UI settings
  ui: {
    primaryColor: '#007AFF',
    secondaryColor: '#FF3B30',
    backgroundColor: '#F5F5F5',
    textColor: '#333333',
    borderRadius: 8,
    // Reduce animations on low performance devices
    animationEnabled: !isLowPerformanceDevice(),
    // Use simplified UI on low performance devices
    useSimplifiedUI: isLowPerformanceDevice(),
  },
  
  // Performance settings
  performance: {
    // Limit number of products displayed on low performance devices
    maxProductsToShow: isLowPerformanceDevice() ? 6 : 20,
    // Reduce image quality on low performance devices
    imageQuality: isLowPerformanceDevice() ? 'low' : 'high',
    // Disable background processes on low performance devices
    enableBackgroundProcesses: !isLowPerformanceDevice(),
    // Limit concurrent network requests on low performance devices
    maxConcurrentRequests: isLowPerformanceDevice() ? 1 : 4,
  }
};

// API endpoints
export const API_ENDPOINTS = {
  products: '/products',
  cart: '/cart',
  orders: '/orders',
  users: '/users',
};

// Navigation routes
export const ROUTES = {
  HOME: 'Home',
  PRODUCT_DETAIL: 'ProductDetail',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  PAYMENT_SUCCESS: 'PaymentSuccess',
  PAYMENT_FAILED: 'PaymentFailed',
}; 