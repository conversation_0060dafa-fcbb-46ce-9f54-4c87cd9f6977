const ngrok = require("ngrok");
const { spawn } = require("child_process");
const os = require("os");
const net = require("net");

// Function to check if a port is open
function checkPort(port) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    socket.setTimeout(1000);
    socket.on("connect", () => {
      socket.destroy();
      resolve(true);
    });
    socket.on("timeout", () => {
      socket.destroy();
      resolve(false);
    });
    socket.on("error", () => {
      resolve(false);
    });
    socket.connect(port, "localhost");
  });
}

// Function to wait for port to be available
async function waitForPort(port, maxWait = 60000) {
  const startTime = Date.now();
  while (Date.now() - startTime < maxWait) {
    if (await checkPort(port)) {
      return true;
    }
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }
  return false;
}

async function startWithNgrok() {
  try {
    console.log("🚀 Starting Expo development server...");

    // Start Expo development server
    const expoProcess = spawn("npx", ["expo", "start", "--dev-client"], {
      stdio: "pipe",
      shell: true,
    });

    // Log Expo output
    expoProcess.stdout.on("data", (data) => {
      console.log(data.toString());
    });

    expoProcess.stderr.on("data", (data) => {
      console.log(data.toString());
    });

    // Wait for Metro bundler to be ready
    console.log("⏳ Waiting for Metro bundler to start...");
    const metroReady = await waitForPort(8081, 60000);
    if (!metroReady) {
      throw new Error("Metro bundler failed to start within 60 seconds");
    }
    console.log("✅ Metro bundler is ready!");

    // Wait for Expo DevTools to be ready
    console.log("⏳ Waiting for Expo DevTools to start...");
    const devToolsReady = await waitForPort(19002, 30000);
    if (!devToolsReady) {
      console.log(
        "⚠️  Expo DevTools not available, continuing with Metro only..."
      );
    } else {
      console.log("✅ Expo DevTools is ready!");
    }

    // Create ngrok tunnel for Metro bundler (default port 8081)
    console.log("🌐 Creating ngrok tunnel for Metro bundler...");
    const metroUrl = await ngrok.connect({
      port: 8081,
      proto: "http",
    });

    let devToolsUrl = null;
    if (devToolsReady) {
      // Create ngrok tunnel for Expo DevTools (default port 19002)
      console.log("🔧 Creating ngrok tunnel for Expo DevTools...");
      devToolsUrl = await ngrok.connect({
        port: 19002,
        proto: "http",
      });
    }

    console.log("\n🎉 Tunnels created successfully!");
    console.log("📱 Metro Bundler URL:", metroUrl);
    if (devToolsUrl) {
      console.log("🔧 Expo DevTools URL:", devToolsUrl);
    }
    console.log("\n📋 Instructions for iPhone:");
    console.log("1. Install Expo Go app on your iPhone from App Store");
    console.log("2. Open Expo Go app");
    console.log("3. Tap 'Enter URL manually'");
    console.log("4. Enter this URL:", metroUrl);
    console.log("5. Tap 'Connect to Development Server'");
    console.log("\n📋 Instructions for Android Emulator:");
    console.log("1. Make sure Android Studio emulator is running");
    console.log("2. Install Expo Go from Play Store in the emulator");
    console.log("3. Open Expo Go and enter the Metro URL:", metroUrl);
    console.log("\n⚠️  Keep this terminal open to maintain the tunnel");
    console.log("🛑 Press Ctrl+C to stop the tunnel and Expo server");

    // Handle cleanup on exit
    process.on("SIGINT", async () => {
      console.log("\n🛑 Shutting down tunnels...");
      await ngrok.kill();
      expoProcess.kill();
      process.exit(0);
    });

    process.on("SIGTERM", async () => {
      console.log("\n🛑 Shutting down tunnels...");
      await ngrok.kill();
      expoProcess.kill();
      process.exit(0);
    });
  } catch (error) {
    console.error("❌ Error starting ngrok tunnel:", error);
    process.exit(1);
  }
}

startWithNgrok();
