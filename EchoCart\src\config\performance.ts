import { Platform } from 'react-native';

// Helper to identify older devices that need performance optimizations
export const isLowPerformanceDevice = (): boolean => {
  // iOS devices older than iOS 13 are considered low performance
  if (Platform.OS === 'ios') {
    const iosVersion = parseInt(Platform.Version as string, 10);
    return iosVersion < 13;
  }
  
  // For Android, we could check API level but for now we'll return false
  return false;
};

// Performance configuration settings
export const performanceConfig = {
  // Reduce animations on low performance devices
  enableAnimations: !isLowPerformanceDevice(),
  
  // Limit number of items rendered at once
  initialRenderCount: isLowPerformanceDevice() ? 4 : 10,
  
  // Use simplified UI components on low performance devices
  useSimplifiedUI: isLowPerformanceDevice(),
  
  // Disable certain features on low performance devices
  enableVoiceFeatures: !isLowPerformanceDevice(),
  
  // Reduce image quality on low performance devices
  imageQuality: isLowPerformanceDevice() ? 'low' : 'high',
  
  // Disable background processes on low performance devices
  enableBackgroundProcesses: !isLowPerformanceDevice(),
};

// Export a function to get performance settings
export const getPerformanceSettings = () => {
  return {
    ...performanceConfig,
    isLowPerformanceDevice: isLowPerformanceDevice(),
  };
}; 