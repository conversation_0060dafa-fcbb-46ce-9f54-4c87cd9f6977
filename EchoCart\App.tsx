import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AppNavigator } from './src/navigation';
import { LogBox, Platform } from 'react-native';

// Disable yellow box warnings in development
if (__DEV__) {
  LogBox.ignoreLogs([
    'Remote debugger',
    'Require cycle:',
    'Possible Unhandled Promise',
    'VirtualizedLists should',
  ]);
}

export default function App() {
  // Optimize performance for older iOS devices
  useEffect(() => {
    if (Platform.OS === 'ios') {
      // Reduce animations for better performance on older devices
      if (parseInt(Platform.Version as string, 10) < 13) {
        console.log('Running on older iOS device, optimizing performance');
      }
    }
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar style="auto" />
      <AppNavigator />
    </SafeAreaProvider>
  );
}
