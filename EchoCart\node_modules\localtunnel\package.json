{"name": "localtunnel", "description": "Expose localhost to the world", "version": "2.0.2", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/localtunnel/localtunnel.git"}, "author": "<PERSON>ylman <<EMAIL>>", "contributors": ["<PERSON>ylman <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "./localtunnel.js", "bin": {"lt": "bin/lt.js"}, "scripts": {"test": "mocha --reporter list --timeout 60000 -- *.spec.js"}, "dependencies": {"axios": "0.21.4", "debug": "4.3.2", "openurl": "1.1.1", "yargs": "17.1.1"}, "devDependencies": {"mocha": "~9.1.1"}, "engines": {"node": ">=8.3.0"}}