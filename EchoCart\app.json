{"expo": {"name": "EchoCart", "slug": "expo-template-blank-typescript", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "jsEngine": "hermes", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSMicrophoneUsageDescription": "This app uses the microphone to enable voice commands for shopping."}, "bundleIdentifier": "com.yourcompany.echocart", "buildNumber": "1.0.0", "requireFullScreen": false}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["RECORD_AUDIO"], "package": "com.yourcompany.echocart", "versionCode": 1}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}]], "extra": {"eas": {"projectId": "your-project-id"}}, "experiments": {"tsconfigPaths": true}}}