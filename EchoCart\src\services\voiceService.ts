import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import { VoiceCommand, ParsedVoiceCommand } from '../types';

// Voice recognition states
export enum VoiceRecognitionState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  ERROR = 'error',
}

// For development, we'll use a simple keyword-based approach
// In production, we would use a more sophisticated NLP solution
export const voiceService = {
  recording: null as Audio.Recording | null,
  state: VoiceRecognitionState.IDLE,
  transcript: '',
  
  /**
   * Start listening for voice input
   */
  startListening: async (): Promise<void> => {
    try {
      // Request permissions
      const { granted } = await Audio.requestPermissionsAsync();
      
      if (!granted) {
        throw new Error('Microphone permission not granted');
      }
      
      // Prepare recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
      
      // Create recording
      const recording = new Audio.Recording();
      try {
        await recording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
        await recording.startAsync();
        
        voiceService.recording = recording;
        voiceService.state = VoiceRecognitionState.LISTENING;
        
        // In a real app, we would stream audio to a speech-to-text service
        // For now, we'll simulate this with a timeout
        setTimeout(() => {
          if (voiceService.state === VoiceRecognitionState.LISTENING) {
            voiceService.stopListening();
          }
        }, 5000);
      } catch (err) {
        console.error('Error starting recording:', err);
        voiceService.state = VoiceRecognitionState.ERROR;
      }
      
    } catch (error) {
      console.error('Failed to start recording', error);
      voiceService.state = VoiceRecognitionState.ERROR;
    }
  },
  
  /**
   * Stop listening and process the voice input
   */
  stopListening: async (): Promise<string> => {
    if (!voiceService.recording) {
      return '';
    }
    
    try {
      voiceService.state = VoiceRecognitionState.PROCESSING;
      
      // Stop recording
      await voiceService.recording.stopAndUnloadAsync();
      
      // Get the recording URI
      const uri = voiceService.recording.getURI();
      voiceService.recording = null;
      
      // In a real app, we would send this audio to a speech-to-text service
      // For now, we'll simulate a response
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // For demo purposes, return a simulated transcript
      // In a real app, this would be the result from the speech-to-text service
      const simulatedTranscripts = [
        'show me wireless headphones',
        'add the smart watch to my cart',
        'show my cart',
        'checkout now',
      ];
      
      const randomIndex = Math.floor(Math.random() * simulatedTranscripts.length);
      const transcript = simulatedTranscripts[randomIndex];
      
      voiceService.transcript = transcript;
      voiceService.state = VoiceRecognitionState.IDLE;
      
      return transcript;
      
    } catch (error) {
      console.error('Failed to stop recording', error);
      voiceService.state = VoiceRecognitionState.ERROR;
      return '';
    }
  },
  
  /**
   * Cancel the current voice recognition
   */
  cancelListening: async (): Promise<void> => {
    if (voiceService.recording) {
      try {
        await voiceService.recording.stopAndUnloadAsync();
        voiceService.recording = null;
      } catch (error) {
        console.error('Failed to cancel recording', error);
      }
    }
    
    voiceService.state = VoiceRecognitionState.IDLE;
    voiceService.transcript = '';
  },
  
  /**
   * Parse the transcript into a command
   */
  parseCommand: (transcript: string): ParsedVoiceCommand => {
    const text = transcript.toLowerCase().trim();
    
    // Search command
    if (text.includes('show me') || text.includes('search for') || text.includes('find')) {
      const searchTerms = text
        .replace('show me', '')
        .replace('search for', '')
        .replace('find', '')
        .trim();
      
      return {
        type: 'search',
        payload: { query: searchTerms }
      };
    }
    
    // Add to cart
    if (text.includes('add') && text.includes('cart')) {
      // Extract product name from command
      let productName = text
        .replace('add', '')
        .replace('to my cart', '')
        .replace('to cart', '')
        .trim();
      
      return {
        type: 'add_to_cart',
        payload: { productName }
      };
    }
    
    // View cart
    if (text.includes('view cart') || text.includes('show cart') || text.includes('my cart')) {
      return {
        type: 'view_cart'
      };
    }
    
    // Checkout
    if (text.includes('checkout') || text.includes('pay now') || text.includes('buy now')) {
      return {
        type: 'checkout'
      };
    }
    
    // Confirm payment
    if (text.includes('confirm') && (text.includes('payment') || text.includes('purchase'))) {
      return {
        type: 'confirm_payment'
      };
    }
    
    // Cancel
    if (text.includes('cancel')) {
      return {
        type: 'cancel'
      };
    }
    
    // Help
    if (text.includes('help')) {
      return {
        type: 'help'
      };
    }
    
    // Unknown command
    return {
      type: 'unknown',
      payload: { transcript: text }
    };
  },
  
  /**
   * Provide voice feedback to the user
   */
  speak: async (text: string): Promise<void> => {
    try {
      return Speech.speak(text, {
        language: 'en',
        pitch: 1.0,
        rate: 0.9,
      });
    } catch (error) {
      console.error('Failed to speak:', error);
    }
  }
}; 