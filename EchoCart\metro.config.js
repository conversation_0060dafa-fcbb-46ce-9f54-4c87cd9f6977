const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Optimize for older devices
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    keep_classnames: true,
    keep_fnames: true,
    mangle: {
      keep_classnames: true,
      keep_fnames: true,
    },
    compress: {
      reduce_funcs: false,
    },
  },
  // Use hermes engine for better performance on older devices
  hermesBytecodeVersion: 0,
};

// Reduce bundle size
config.resolver = {
  ...config.resolver,
  sourceExts: ['jsx', 'js', 'ts', 'tsx', 'json'],
  assetExts: ['png', 'jpg', 'jpeg', 'gif'],
};

// Increase max workers for faster bundling
config.maxWorkers = 2;

module.exports = config; 