export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  isAnonymous: boolean;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: Date;
}

export interface VoiceState {
  isListening: boolean;
  transcript: string;
  error: string | null;
}

export interface AppState {
  user: User | null;
  cart: CartItem[];
  voiceState: VoiceState;
}

export type VoiceCommand = 
  | 'search'
  | 'add_to_cart'
  | 'remove_from_cart'
  | 'view_cart'
  | 'checkout'
  | 'confirm_payment'
  | 'cancel'
  | 'help'
  | 'unknown';

export interface ParsedVoiceCommand {
  type: VoiceCommand;
  payload?: any;
} 