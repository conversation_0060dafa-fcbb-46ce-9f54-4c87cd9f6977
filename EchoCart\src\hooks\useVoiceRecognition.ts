import { useState, useCallback, useEffect } from 'react';
import { voiceService, VoiceRecognitionState } from '../services/voiceService';
import { ParsedVoiceCommand } from '../types';

interface UseVoiceRecognitionResult {
  isListening: boolean;
  transcript: string;
  state: VoiceRecognitionState;
  startListening: () => Promise<void>;
  stopListening: () => Promise<string>;
  cancelListening: () => Promise<void>;
  parsedCommand: ParsedVoiceCommand | null;
  resetCommand: () => void;
}

export const useVoiceRecognition = (): UseVoiceRecognitionResult => {
  const [state, setState] = useState<VoiceRecognitionState>(VoiceRecognitionState.IDLE);
  const [transcript, setTranscript] = useState<string>('');
  const [parsedCommand, setParsedCommand] = useState<ParsedVoiceCommand | null>(null);
  
  // Update state when voiceService state changes
  useEffect(() => {
    const checkState = () => {
      setState(voiceService.state);
      setTranscript(voiceService.transcript);
    };
    
    // Check state every 100ms
    const interval = setInterval(checkState, 100);
    
    return () => {
      clearInterval(interval);
    };
  }, []);
  
  const startListening = useCallback(async () => {
    setState(VoiceRecognitionState.LISTENING);
    setTranscript('');
    setParsedCommand(null);
    await voiceService.startListening();
  }, []);
  
  const stopListening = useCallback(async () => {
    setState(VoiceRecognitionState.PROCESSING);
    const result = await voiceService.stopListening();
    setTranscript(result);
    
    // Parse the command
    if (result) {
      const command = voiceService.parseCommand(result);
      setParsedCommand(command);
    }
    
    setState(VoiceRecognitionState.IDLE);
    return result;
  }, []);
  
  const cancelListening = useCallback(async () => {
    await voiceService.cancelListening();
    setState(VoiceRecognitionState.IDLE);
    setTranscript('');
    setParsedCommand(null);
  }, []);
  
  const resetCommand = useCallback(() => {
    setParsedCommand(null);
  }, []);
  
  return {
    isListening: state === VoiceRecognitionState.LISTENING,
    transcript,
    state,
    startListening,
    stopListening,
    cancelListening,
    parsedCommand,
    resetCommand,
  };
}; 